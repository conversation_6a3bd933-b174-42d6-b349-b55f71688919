import {
  YTT<PERSON><PERSON>,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { router } from '@jgl/utils';
import { memo, useCallback, useMemo } from 'react';
import { FlatList } from 'react-native';
import {
  getTargetBookCurrentCachedPageItemCode,
  updateTargetBookCurrentPage,
} from '../helper/StudyDataHelper';
import { useBookRouterParams } from '../hooks/useBookRouterParams';
import { StudyHeaderPageOrderItem } from '../study/StudyHeaderPageOrderItem';
import { type PageWithChapter } from '../types/AiBookTypes';
import { isEmpty } from 'lodash-es';

type PageGridListProps = {
  pageWithChapters?: PageWithChapter[];
};

/**
 * 图书详情页的页码列表
 */
export const PageGridList = memo((props: PageGridListProps) => {
  const { pageWithChapters = [] } = props;
  const params = useBookRouterParams();

  const { bookId, currentPageId, currentChapterId, ...restParams } = params;

  const onPressPageNumberItem = useCallback(
    async (item: PageWithChapter) => {
      const currentPageId = item.page.id ?? 0;
      const currentChapterId = item.chapter.id ?? 0;
      const currentQuestionId = await getTargetBookCurrentCachedPageItemCode(
        Number(bookId),
        currentChapterId,
        currentPageId,
      );
      router.push('/book/study', {
        ...restParams,
        bookId,
        currentPageId,
        currentChapterId,
        currentQuestionId,
      });
      requestAnimationFrame(() => {
        // 更新缓存
        updateTargetBookCurrentPage(Number(bookId), {
          currentPageId: Number(currentPageId),
          currentChapterId: Number(currentChapterId),
        });
      });
    },
    [bookId, restParams],
  );

  const selectedItem = useMemo(
    () =>
      pageWithChapters
        .map((item) => ({
          pageId: item.page.id ?? 0,
          chapterId: item.chapter.id ?? 0,
        }))
        .find(
          (item) =>
            String(item.pageId) === currentPageId &&
            String(item.chapterId) === currentChapterId,
        ),
    [currentChapterId, currentPageId, pageWithChapters],
  );

  const keyExtractor = useCallback((item: PageWithChapter, index: number) => {
    return `${item.page.id}-${index}`;
  }, []);

  const renderItem = useCallback(
    ({ item, index }: { item: PageWithChapter; index: number }) => {
      return (
        <StudyHeaderPageOrderItem
          item={item}
          index={index}
          selectedItem={selectedItem}
          onPressPageNumberItem={onPressPageNumberItem}
        />
      );
    },
    [onPressPageNumberItem, selectedItem],
  );

  return (
    <YTYStack gap={8} flexShrink={1} mb={8}>
      <YTText color='$color12' fontSize={16} fontWeight='bold'>
        页码
      </YTText>
      <FlatList
        data={pageWithChapters?.filter((item) => !isEmpty(item.page.pageNo))}
        horizontal={false} // 确保纵向排列
        renderItem={renderItem}
        numColumns={0}
        showsVerticalScrollIndicator={false}
        initialNumToRender={pageWithChapters.length}
        renderToHardwareTextureAndroid
        contentContainerStyle={{
          flexDirection: 'row', // 横向排列
          flexWrap: 'wrap', // 自动换行
          paddingVertical: 8,
          display: 'flex',
          columnGap: 16,
          rowGap: 16,
          alignItems: 'flex-start', // 顶部对齐
        }}
        keyExtractor={keyExtractor}
      />
      <YTView />
    </YTYStack>
  );
});
