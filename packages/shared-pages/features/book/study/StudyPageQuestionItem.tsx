import {
  YTImage,
  YTStateView,
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import DropShadow from 'react-native-drop-shadow';
import { type SharedValue } from 'react-native-reanimated';
import { useStudyPageQuestionItem } from '../hooks/useStudyPageQuestionItem';
import { ResType } from '../types/AiBookEnum';
import { type PageWithChapter } from '../types/AiBookTypes';
import { StudyPageSingleQuestion } from './StudyPageSingleQuestion';
import { StudyQuestionNumberList } from './StudyQuestionNumberList';
import { StudyResTag } from './StudyResTag';
import { ActivityIndicator } from 'react-native';

type StudyPageQuestionItemProps = {
  item: PageWithChapter;
  index: number;
  animationValue: SharedValue<number>;
};
/**
 * 当前页中题目内容
 * @param props
 * @returns
 */
export const StudyPageQuestionItem = (props: StudyPageQuestionItemProps) => {
  const { item } = props;

  const {
    isLoading,
    error,
    imageHeight,
    imageWidth,
    scaleX,
    scaleY,
    pageItems,
    currentItemPosition,
    imgUrl,
    onQuestionItemChanged,
    retry,
    isShowVideoTag,
    isShowAudioTag,
    handlePressCoord,
    onLayout,
  } = useStudyPageQuestionItem({ item });

  return (
    <YTYStack flex={1}>
      <StudyQuestionNumberList
        pageItems={pageItems}
        onQuestionItemChanged={onQuestionItemChanged}
      />
      <YTStateView
        flex={1}
        isLoading={imageHeight === 0 || imageWidth === 0 || isLoading}
        error={error}
        onRetry={retry}
      >
        <DropShadow
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            marginTop: 12,
            display: 'flex',
            marginHorizontal: 16,
            flexDirection: 'row',
          }}
        >
          <YTYStack
            position='relative'
            aspectRatio={imageWidth / imageHeight}
            w={'100%'}
            alignItems='center'
            justifyContent={scaleX === 0 ? 'center' : 'flex-start'}
            onLayout={onLayout}
          >
            {currentItemPosition && imgUrl && scaleX > 0 ? (
              <StudyPageSingleQuestion
                position={currentItemPosition}
                scaleX={scaleX}
                scaleY={scaleY}
                imageWidth={imageWidth}
                imageHeight={imageHeight}
                imgUrl={imgUrl}
              />
            ) : scaleX === 0 ? (
              <ActivityIndicator />
            ) : (
              <YTImage
                source={imgUrl}
                w={'100%'}
                aspectRatio={imageWidth / imageHeight}
                contentFit='cover'
              />
            )}
            {!currentItemPosition &&
              scaleX > 0 &&
              pageItems.map((pageItem, idx) => {
                const [x1 = 0, y1 = 0, x2 = 0, y2 = 0] =
                  pageItem.position ?? [];
                // 是否显示视频标签
                const showVideoTag = isShowVideoTag(idx);
                // 是否显示音频标签
                const showAudioTag = isShowAudioTag(idx);
                return (
                  <YTTouchable
                    key={idx}
                    position='absolute'
                    left={x1 * imageWidth * scaleX}
                    top={y1 * imageHeight * scaleY}
                    w={(x2 - x1) * imageWidth * scaleX}
                    h={(y2 - y1) * imageHeight * scaleY}
                    borderWidth={1}
                    borderColor='$accent9'
                    borderRadius={4}
                    pointerEvents='box-none'
                    onPress={() => {
                      handlePressCoord(idx);
                    }}
                  >
                    <YTXStack position='absolute' top={0} right={0}>
                      {showVideoTag && <StudyResTag resType={ResType.VIDEO} />}
                      {showAudioTag && <StudyResTag resType={ResType.SOUND} />}
                    </YTXStack>
                  </YTTouchable>
                );
              })}
          </YTYStack>
        </DropShadow>
      </YTStateView>
    </YTYStack>
  );
};
