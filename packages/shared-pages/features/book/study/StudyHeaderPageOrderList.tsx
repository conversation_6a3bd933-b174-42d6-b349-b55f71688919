import {
  Y<PERSON><PERSON><PERSON>,
  Y<PERSON>iew,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { Fragment, useCallback, useMemo, useRef } from 'react';
import { FlatList } from 'react-native';
import {
  runOnJS,
  useAnimatedReaction,
  type SharedValue,
} from 'react-native-reanimated';
import { type Chapter, type Page } from '../dtos/AiBookDTO';
import { useCurrentPage } from '../hooks/useCurrentPage';
import { type PageWithChapter } from '../types/AiBookTypes';
import { StudyHeaderPageOrderItem } from './StudyHeaderPageOrderItem';
import { isEmpty } from 'lodash-es';

type StudyHeaderPageOrderListProps = {
  pageData?: PageWithChapter[];
  progress: SharedValue<number>;
  onPressPageNumberItem?: (data: PageWithChapter) => void;
};

/**
 * 智慧图书学习页面顶部页码列表
 * @returns
 */
export const StudyHeaderPageOrderList = (
  props: StudyHeaderPageOrderListProps,
) => {
  const { pageData = [], progress, onPressPageNumberItem } = props;

  const flatListRef = useRef<FlatList<PageWithChapter>>(null);

  const { currentPage: currentPageWithChapter } = useCurrentPage();

  const currentIndex = useMemo(() => {
    return pageData.findIndex(
      (item) =>
        item.chapter.id === currentPageWithChapter?.chapter.id &&
        item.page.id === currentPageWithChapter?.page.id,
    );
  }, [
    pageData,
    currentPageWithChapter?.chapter.id,
    currentPageWithChapter?.page.id,
  ]);

  // 提前定义共享的scroll函数
  const scrollToIndex = useCallback(
    (params: { index: number; viewPosition: number; animated: boolean }) => {
      if (flatListRef.current) {
        flatListRef.current?.scrollToIndex(params);
      } else {
        const wait = new Promise((resolve) => setTimeout(resolve, 500));
        wait.then(() => {
          flatListRef.current?.scrollToIndex({
            index: params.index,
            viewPosition: 0.5,
            animated: true,
          });
        });
      }
    },
    [],
  );

  useAnimatedReaction(
    () => progress.value,
    (value) => {
      'worklet';

      if (
        value > -1 &&
        (currentIndex - value < -0.8 ||
          currentIndex - value > 0.8 ||
          Number.isInteger(value))
      ) {
        let index = value;
        if (currentIndex - value < -0.8) {
          index = currentIndex + 1;
        } else if (currentIndex - value > 0.8) {
          index = currentIndex - 1;
        }
        runOnJS(scrollToIndex)({
          index,
          viewPosition: 0.5,
          animated: true,
        });
      }
    },
  );

  const renderItem = useCallback(
    ({ item, index }: { item: PageWithChapter; index: number }) => {
      const { pageNo } = item.page;
      if (isEmpty(pageNo)) {
        return <YTXStack bg={'transparent'} />;
      }
      return (
        <YTXStack px={8} bg={'transparent'}>
          <StudyHeaderPageOrderItem
            onPressPageNumberItem={onPressPageNumberItem}
            item={item}
            index={index}
            progress={progress}
          />
        </YTXStack>
      );
    },
    [onPressPageNumberItem, progress],
  );

  const keyExtractor = useCallback(
    (item: { page: Page; chapter: Chapter }, index: number) => {
      return `${item.page.pageNo?.toString()}-${index}`;
    },
    [],
  );

  return (
    <YTYStack flex={1}>
      <FlatList
        ref={flatListRef}
        data={pageData}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        initialNumToRender={50}
        maxToRenderPerBatch={50}
        onScrollToIndexFailed={(info) => {
          const { highestMeasuredFrameIndex } = info;
          if (highestMeasuredFrameIndex) {
            // 分段加载
            flatListRef.current?.scrollToIndex({
              index: highestMeasuredFrameIndex,
              viewPosition: 0.5,
              animated: false,
            });
            setTimeout(() => {
              flatListRef.current?.scrollToIndex({
                index: currentIndex,
                viewPosition: 0.5,
                animated: true,
              });
            }, 500);
            return;
          }
          const wait = new Promise((resolve) => setTimeout(resolve, 500));
          wait.then(() => {
            flatListRef.current?.scrollToIndex({
              index: currentIndex,
              viewPosition: 0.5,
              animated: true,
            });
          });
        }}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 2,
          paddingVertical: 8,
        }}
      />
    </YTYStack>
  );
};
