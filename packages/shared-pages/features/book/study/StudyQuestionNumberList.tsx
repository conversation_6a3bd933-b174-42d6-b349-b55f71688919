import { YTText, YTTouchable, YTView } from '@bookln/cross-platform-components';
import { isEmpty } from 'lodash-es';
import { memo, useCallback, useMemo } from 'react';
import { FlatList } from 'react-native';
import { arabicToChineseSimple } from '../../../utils/numberUtils';
import { type PageItem } from '../dtos/AiBookDTO';
import { useBookRouterParams } from '../hooks/useBookRouterParams';
import { type PageWithChapter } from '../types/AiBookTypes';

type StudyQuestionNumberListProps = {
  /**
   * 题号列表项
   */
  pageItems: PageItem[];

  /**
   * 题号变化
   */
  onQuestionItemChanged: (item?: PageItem | undefined) => void;
};

/**
 * 题号列表
 * @returns
 */
export const StudyQuestionNumberList = memo(
  (props: StudyQuestionNumberListProps) => {
    const { pageItems = [], onQuestionItemChanged } = props;

    const { currentQuestionId } = useBookRouterParams();

    const renderItem = useCallback(
      ({ item, index }: { item: PageItem; index: number }) => {
        return (
          <StudyQuestionNumberItem
            item={item}
            index={index}
            onQuestionItemChanged={onQuestionItemChanged}
          />
        );
      },
      [onQuestionItemChanged],
    );

    /**
     * 分割线
     */
    const renderSeparator = useCallback(() => {
      return <YTView w={12} bg={'transparent'} />;
    }, []);

    const keyExtractor = useCallback((item: PageItem, index: number) => {
      return `${item.itemCode?.toString() ?? ''}-${index}`;
    }, []);

    const onPressWholePage = useCallback(() => {
      onQuestionItemChanged(undefined);
    }, [onQuestionItemChanged]);

    /**
     * 整页
     */
    const ListHeaderComponent = useMemo(() => {
      const isFocused = currentQuestionId === undefined;
      return (
        <YTTouchable
          onPress={onPressWholePage}
          px={12}
          borderRadius={8}
          py={4}
          mr={12}
          borderWidth={1}
          borderStyle='solid'
          borderColor={isFocused ? '$accent6' : '$slBackground'}
          bg={isFocused ? '$blue2' : '$slBackground'}
        >
          <YTText
            color={isFocused ? '$accent9' : '$color12'}
            fontSize={14}
            fontWeight={isFocused ? 'bold' : '$4'}
          >
            整页
          </YTText>
        </YTTouchable>
      );
    }, [currentQuestionId, onPressWholePage]);

    return (
      <YTView w={'100%'} minHeight={32}>
        <FlatList
          showsHorizontalScrollIndicator={false}
          ItemSeparatorComponent={renderSeparator}
          horizontal
          ListHeaderComponent={
            pageItems.length > 0 ? ListHeaderComponent : undefined
          }
          renderItem={renderItem}
          data={pageItems}
          contentContainerStyle={{ paddingHorizontal: 16 }}
          keyExtractor={keyExtractor}
        />
      </YTView>
    );
  },
);

type StudyQuestionNumberItemProps = {
  item: PageItem;
  index: number;
  onQuestionItemChanged: (item?: PageItem | undefined) => void;
};

/**
 * 题号列表项
 */
const StudyQuestionNumberItem = memo((props: StudyQuestionNumberItemProps) => {
  const { item, index, onQuestionItemChanged } = props;

  const { currentQuestionId } = useBookRouterParams();
  const { itemName = '' } = item;

  const isFocused = useMemo(
    () => currentQuestionId === item.itemCode,
    [currentQuestionId, item.itemCode],
  );

  const onPress = useCallback(() => {
    onQuestionItemChanged(item);
  }, [item, onQuestionItemChanged]);

  return (
    <YTTouchable
      onPress={onPress}
      px={12}
      borderRadius={8}
      py={4}
      borderWidth={1}
      borderStyle='solid'
      borderColor={isFocused ? '$accent6' : '$slBackground'}
      bg={isFocused ? '$blue2' : '$slBackground'}
    >
      <YTText
        color={isFocused ? '$accent9' : '$color12'}
        fontSize={14}
        fontWeight={isFocused ? 'bold' : '$4'}
      >
        {isEmpty(itemName) ? arabicToChineseSimple(index + 1) : itemName}
      </YTText>
    </YTTouchable>
  );
});
