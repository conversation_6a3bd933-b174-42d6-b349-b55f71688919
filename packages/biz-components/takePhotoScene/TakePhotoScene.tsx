import { successVibrate, useSafeAreaInsets } from '@jgl/biz-func';
import Icon from '@jgl/icon';
import { showToast } from '@jgl/utils';
import * as ImageManipulator from 'expo-image-manipulator';
import * as ImagePicker from 'expo-image-picker';
import { useFocusEffect, useNavigation } from 'expo-router';

import { PermissionEnum, PermissionHooks } from '@bookln/permission';
import { JglText, JglTouchable, JglXStack, JglYStack } from '@jgl/ui-v4';
import type { EventArg } from '@react-navigation/native';
import { isEmpty } from 'lodash-es';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { type LayoutChangeEvent, StyleSheet, View } from 'react-native';
import Canvas from 'react-native-canvas';
import { Image } from 'tamagui';
import { CutScene } from '../cutScene/CutScene';
import type { TakePhotoSceneProps } from './TakePhotoScene.type';
import { Camera, useCameraDevice } from 'react-native-vision-camera';

export const TakePhotoScene = memo((props: TakePhotoSceneProps) => {
  const { visible, scene, needCut, onCommitPhoto } = props;

  const cameraRef = useRef<Camera>(null);

  const [flashMode, setFlashMode] = useState<'on' | 'off'>('off');

  const [mode, setMode] = useState<'page' | 'question'>('page');

  const [isActive, setIsActive] = useState(true);

  const navigation = useNavigation();

  const [originalPickPhoto, setOriginalPickPhoto] = useState<string>();

  const { bottom } = useSafeAreaInsets();

  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const [canvasSize, setCanvasSize] = useState<{
    width: number;
    height: number;
  }>();

  const handleGoBackToTakePhotoScene = useCallback(() => {
    setOriginalPickPhoto(undefined);
  }, []);

  const handleBeforeRemove = useCallback(
    (
      e: EventArg<
        'beforeRemove',
        true,
        {
          action: Readonly<{
            type: string;
            payload?: object | undefined;
            source?: string | undefined;
            target?: string | undefined;
          }>;
        }
      >,
    ) => {
      const { type } = e.data.action;
      e.preventDefault();
      if (originalPickPhoto && type !== 'POP') {
        handleGoBackToTakePhotoScene();
      } else {
        navigation.dispatch(e.data.action);
      }
    },
    [handleGoBackToTakePhotoScene, navigation, originalPickPhoto],
  );

  useEffect(() => {
    navigation.addListener('beforeRemove', handleBeforeRemove);
    return () => {
      navigation.removeListener('beforeRemove', handleBeforeRemove);
    };
  }, [handleBeforeRemove, navigation]);

  const handleStartCropping = useCallback(
    async (uri: string) => {
      // 压缩图片
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 1000 } }], // 限制宽度为1000像素，高度等比缩放
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG },
      );
      if (needCut || mode === 'question') {
        setOriginalPickPhoto(manipulatedImage.uri);
      } else {
        onCommitPhoto?.(manipulatedImage.uri);
      }
    },
    [mode, needCut, onCommitPhoto],
  );

  /** 相册选取 */
  const handleClickPhotoLibrary = useCallback(async () => {
    const result = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: scene,
    });
    if (result) {
      const pickResult = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        aspect: [4, 3],
        quality: 0.8,
      });
      const [asset] = pickResult.assets ?? [];
      if (asset) {
        handleStartCropping(asset.uri);
      }
    } else {
      showToast({ title: '没有访问相册的权限' });
    }
  }, [checkAndRequestPermission, handleStartCropping, scene]);

  /** 照相 */
  const handleClickTakePhoto = useCallback(async () => {
    successVibrate();
    const snapshot = await cameraRef.current
      ?.takePhoto({
        enableShutterSound: false,
        flash: flashMode,
      })
      .catch((e) => {
        console.log('buyu ~ 🚀 ~ e=>', e);
      });

    const { path } = snapshot ?? {};
    if (path) {
      handleStartCropping(path);
    }
  }, [flashMode, handleStartCropping]);

  /** 打开闪光灯 */
  const handleClickLight = useCallback(() => {
    setFlashMode((prev) => {
      if (prev === 'off') {
        return 'on';
      }
      return 'off';
    });
  }, []);

  useFocusEffect(() => {
    // 获得焦点
    setIsActive(true);
    return () => {
      // 失去焦点
      setIsActive(false);
    };
  });

  /** 底部操作区域 */
  const renderBottom = useMemo(() => {
    return (
      <JglXStack
        pos='relative'
        flexDirection='row'
        alignItems='center'
        justifyContent='space-between'
        px={32}
        pt={33}
        backgroundColor='black'
        style={{
          paddingBottom: bottom + 33,
          display: !isEmpty(originalPickPhoto) ? 'none' : 'flex',
        }}
      >
        <JglTouchable
          jglClassName='my-auto rounded-full'
          onPress={handleClickPhotoLibrary}
        >
          <Image
            source={require('../assets/images/ic_album.png')}
            width={44}
            height={44}
          />
        </JglTouchable>
        <JglTouchable
          jglClassName='flex-center relative'
          onPress={handleClickTakePhoto}
        >
          <Image
            source={{ uri: Icon.takePhotoReadingTakePhoto }}
            width={64}
            height={64}
          />
        </JglTouchable>

        <JglTouchable
          jglClassName='my-auto rounded-full'
          onPress={handleClickLight}
        >
          <Image
            source={
              flashMode === 'on'
                ? require('../assets/images/ic_light_on.png')
                : require('../assets/images/ic_light_off.png')
            }
            width={44}
            height={44}
          />
        </JglTouchable>
      </JglXStack>
    );
  }, [
    bottom,
    originalPickPhoto,
    handleClickPhotoLibrary,
    handleClickTakePhoto,
    handleClickLight,
    flashMode,
  ]);

  /** 绘制参考线 */
  const handleCanvas = useCallback(
    (canvas: Canvas) => {
      if (!canvas || !canvasSize) return;
      const { width, height } = canvasSize;
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 0.5;
      ctx.beginPath();
      ctx.moveTo(width / 3, 0);
      ctx.lineTo(width / 3, height);
      ctx.stroke();
      ctx.moveTo((width / 3) * 2, 0);
      ctx.lineTo((width / 3) * 2, height);
      ctx.stroke();
      ctx.moveTo(0, height / 3);
      ctx.lineTo(width, height / 3);
      ctx.stroke();
      ctx.moveTo(0, (height / 3) * 2);
      ctx.lineTo(width, (height / 3) * 2);
      ctx.stroke();

      ctx.font = '16px sans-serif';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('平行纸面，文字对齐参考线', width / 2, height / 2 - 12);
    },
    [canvasSize],
  );

  const renderCanvas = useMemo(() => {
    if (canvasSize) {
      return (
        <View pointerEvents='none' style={StyleSheet.absoluteFill}>
          <Canvas ref={handleCanvas} />
        </View>
      );
    }
  }, [canvasSize, handleCanvas]);

  const onLayout = useCallback((event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setCanvasSize({ width, height });
  }, []);

  const handleClickSwitchMode = useCallback(() => {
    setMode((prev) => (prev === 'page' ? 'question' : 'page'));
  }, []);

  /** 切换模式 */
  const renderSwitchMode = useCallback(() => {
    return (
      <JglTouchable
        pos='absolute'
        alignSelf='center'
        display={!isEmpty(originalPickPhoto) ? 'none' : 'flex'}
        bottom={10}
        zIndex={1000}
        bg='#0000007F'
        flexDirection='row'
        borderRadius={100}
        overflow='hidden'
        onPress={handleClickSwitchMode}
        p={2}
        style={{ minHeight: 0 }}
      >
        <JglXStack
          px={12}
          py={2}
          borderRadius={100}
          bg={mode === 'page' ? '#FFFFFF4D' : 'transparent'}
        >
          <JglText color='white' fontSize={14}>
            自动识别
          </JglText>
        </JglXStack>
        <JglXStack
          px={12}
          py={2}
          borderRadius={100}
          bg={mode === 'question' ? '#FFFFFF4D' : 'transparent'}
        >
          <JglText color='white' fontSize={14}>
            手动框选
          </JglText>
        </JglXStack>
      </JglTouchable>
    );
  }, [handleClickSwitchMode, mode, originalPickPhoto]);

  const cameraDevice = useCameraDevice('back');

  if (!visible) {
    return null;
  } else {
    return (
      <JglYStack jglClassName='relative flex h-full w-full flex-1'>
        <JglYStack flex={1}>
          {cameraDevice && (
            <Camera
              ref={cameraRef}
              style={{
                width: '100%',
                height: '100%',
                backgroundColor: 'black',
              }}
              photo
              torch={flashMode}
              onLayout={!canvasSize ? onLayout : undefined}
              device={cameraDevice}
              isActive={isActive}
            />
          )}
          {renderCanvas}
          {renderSwitchMode()}
        </JglYStack>
        {renderBottom}
        <CutScene
          photoLocalPath={originalPickPhoto}
          visible={!isEmpty(originalPickPhoto)}
          onCommitPhoto={onCommitPhoto}
        />
      </JglYStack>
    );
  }
});
